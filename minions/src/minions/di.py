import os

from dependency_injector import containers, providers
from dotenv import load_dotenv

from minions.config.model import APPENV, AppConfig
from minions.domain.disk.svc import DiskSvc
from minions.domain.ingest.svc import IngestSvc
from minions.domain.tdb.svc import TdbSvc


class DI(containers.DeclarativeContainer):
    config = providers.Configuration()
    app_config = providers.Singleton(AppConfig.model_validate, config)
    tdb_svc = providers.Singleton(TdbSvc, meta_cfg=app_config.provided.meta)

    disk_svc = providers.Singleton(DiskSvc, meta_cfg=app_config.provided.meta)

    ingest_svc = providers.Singleton(
        IngestSvc,
        meta_cfg=app_config.provided.meta,
        tdb_svc=tdb_svc,
        disk_svc=disk_svc,
    )


def setup_di() -> DI:
    load_dotenv()
    app_env = APPENV(os.environ.get("APP_ENV"))

    di = DI()

    di.config.from_yaml("./config/configs.dev.yml", required=True)
    if app_env != APPENV.dev:
        di.config.from_yaml(f"./config/configs.{app_env.value}.yml", required=False)

    di.check_dependencies()

    return di
