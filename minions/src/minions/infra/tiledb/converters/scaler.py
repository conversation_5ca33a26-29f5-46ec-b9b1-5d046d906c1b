from __future__ import annotations

from concurrent import futures
from typing import TYPE_CHECKING

import cv2
import numpy as np
import psutil

from minions.infra.tiledb.converters.tiles import (
    iter_slices_by_clamp,
)
from minions.infra.tiledb.cv import OPEN_CV_DSIZE_XY_AXES, OPEN_CV_IMG_AXES
from minions.infra.tiledb.readers.base import AxesData

if TYPE_CHECKING:
    import tiledb

    from minions.infra.tiledb.axes import AxesMapper


class Scaler:
    def __init__(
        self,
        max_workers: int | None = None,
    ):
        """chunked: if True apply scale by tiles."""
        self.max_workers = max_workers
        # spawn here is for tiledb multithreading write
        self._executor = (
            futures.ThreadPoolExecutor(max_workers) if max_workers != 0 else None
        )

        # as we are using opencv resize impl,
        # it is alread multi-threading, so we force it to None here.
        # if one day we are changing the impl, we can delete it
        self._executor = None

    def apply(
        self,
        src_tdb: tiledb.Array,
        tgt_tdb: tiledb.Array,
    ) -> None:
        src_axes_shape = _get_axes_shape_from_domain(src_tdb.domain)
        tgt_axes_shape = _get_axes_shape_from_domain(tgt_tdb.domain)

        src_to_open_cv = src_axes_shape.axes.mapper(OPEN_CV_IMG_AXES)
        open_cv_to_tgt = OPEN_CV_IMG_AXES.mapper(tgt_axes_shape.axes)
        tgt_to_xy = tgt_axes_shape.axes.mapper(OPEN_CV_DSIZE_XY_AXES)
        clamped_mem = self._estimate_clamped_mem()
        scale_kwargs = {
            "src_tdb": src_tdb,
            "tgt_tdb": tgt_tdb,
            "src_axes_shape": src_axes_shape,
            "tgt_axes_shape": tgt_axes_shape,
            "tgt_to_xy": tgt_to_xy,
            "src_to_open_cv": src_to_open_cv,
            "open_cv_to_tgt": open_cv_to_tgt,
        }

        if self._executor:
            fs = [
                self._executor.submit(
                    _scale,
                    tgt_tile_slices=tgt_tile_slices,
                    **scale_kwargs,
                )
                for tgt_tile_slices in iter_slices_by_clamp(
                    tgt_axes_shape,
                    tgt_tdb.dtype,
                    clamped_mem,  # as we need read src by factor which generally is 2*2
                )
            ]
            for f in futures.as_completed(fs):
                f.result()
        else:
            for tgt_tile_slices in iter_slices_by_clamp(
                tgt_axes_shape,
                tgt_tdb.dtype,
                clamped_mem,  # as we need read src by factor which generally is 2*2
            ):
                _scale(tgt_tile_slices=tgt_tile_slices, **scale_kwargs)

    def _estimate_clamped_mem(self):
        # as we need read src by factor which generally is 2*2, so // 4
        # and opencv will create the source tdb array,
        # and consumption is about 3x, so // 3
        clamped_mem = psutil.virtual_memory().available * 0.8 // 4 // 3
        if self.max_workers is not None and self.max_workers > 0:
            clamped_mem = clamped_mem // self.max_workers
        return int(clamped_mem)


def _scale(  # noqa: PLR0913
    src_tdb: tiledb.DenseArray,
    tgt_tdb: tiledb.DenseArray,
    src_axes_shape: AxesData[int],
    tgt_axes_shape: AxesData[int],
    tgt_to_xy: AxesMapper,
    src_to_open_cv: AxesMapper,
    open_cv_to_tgt: AxesMapper,
    tgt_tile_slices: tuple[slice, ...] | None = None,
):
    if tgt_tile_slices is None:
        src_img_array = src_tdb[:]
        tgt_shape = tgt_axes_shape.data

        tgt_img_array = _opencv_resize(
            src_img_array,
            tgt_shape,
            src_to_open_cv,
            tgt_to_xy,
            open_cv_to_tgt,
        )
        tgt_tdb[:] = tgt_img_array

    else:
        src_img_tile_slices = []

        for dim_idx, dim in enumerate(src_axes_shape.axes.dims):
            tgt_tile_slice = tgt_tile_slices[dim_idx]
            src_shape = src_axes_shape.raw[dim]
            scale_factor = src_shape / tgt_axes_shape.raw[dim]

            start = int(tgt_tile_slice.start * scale_factor)
            stop = int(min(tgt_tile_slice.stop * scale_factor, src_shape))
            src_img_tile_slices.append(slice(start, stop))

        src_img_array = src_tdb[*src_img_tile_slices]
        tgt_shape = tuple(s.stop - s.start for s in tgt_tile_slices)

        tgt_img_array = _opencv_resize(
            src_img_array,
            tgt_shape,
            src_to_open_cv,
            tgt_to_xy,
            open_cv_to_tgt,
        )

        tgt_tdb[tgt_tile_slices] = tgt_img_array


def _get_axes_shape_from_domain(domain: tiledb.Domain):
    axes_shape = AxesData[int].from_dict(
        {dim.name: int(dim.domain[1]) - int(dim.domain[0]) + 1 for dim in domain},
    )

    return axes_shape


def _opencv_resize(
    src_img_array: np.ndarray,
    tgt_shape: tuple[int, ...],
    src_to_open_cv: AxesMapper,
    tgt_to_xy: AxesMapper,
    open_cv_to_tgt: AxesMapper,
) -> np.ndarray:
    tgt_shape_in_xy = tgt_to_xy.map_shape(tgt_shape)
    src_img_cv = src_to_open_cv.map_array(src_img_array)

    # NOTE! cv2.resize is alread multithreading,
    # so no need to run multi-threading in python caller
    resized_open_cv = cv2.resize(
        # src img should be in  y,x,c order
        src=src_img_cv,
        # dsize should be in width,height (x,y) order
        dsize=tgt_shape_in_xy,
        # inter exact here is need otherwise we need to convert src to float
        # don't use INTER_NEAREST_EXACT, as it has bug will cause Segmentation Fault
        # https://github.com/opencv/opencv/issues/27191
        # also https://github.com/opencv/opencv/issues/25018
        # use INTER_LINEAR_EXACT instead INTER_LINEAR
        # is for some speed and exact size match
        interpolation=cv2.INTER_LINEAR_EXACT,
    )

    two_dim = 2
    if resized_open_cv.ndim == two_dim:
        # expand the C dim for axes mapper
        resized_open_cv = np.expand_dims(resized_open_cv, 2)
    tgt_array = open_cv_to_tgt.map_array(resized_open_cv)
    return tgt_array
