from __future__ import annotations

import os
from pathlib import Path
from typing import TYPE_CHECKING, Self
from urllib.parse import urlparse

import tiledb

if TYPE_CHECKING:
    from collections.abc import Callable


class ReadWriteGroup:
    def __init__(
        self,
        uri: str,
        config: tiledb.Config | None = None,
    ):
        self.ctx = tiledb.Ctx(config)

        parsed_uri = urlparse(uri)
        # normalize uri if it's a local path (e.g. ../..foo/bar)

        # Windows paths produce single letter scheme matching the drive letter
        # Unix absolute path produce an empty scheme
        if len(parsed_uri.scheme) < 2 or parsed_uri.scheme == "file":
            uri = str(Path(parsed_uri.path).resolve()).replace("\\", "/")
        if tiledb.object_type(uri, ctx=self.ctx) != "group":
            tiledb.group_create(uri, ctx=self.ctx)
        self._uri = uri if uri.endswith("/") else uri + "/"
        self._is_cloud = parsed_uri.scheme == "tiledb"

    def __enter__(self) -> Self:
        self.r_group = tiledb.Group(self._uri, "r", ctx=self.ctx)
        self.w_group = tiledb.Group(self._uri, "w", ctx=self.ctx)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        self.r_group.close()
        self.w_group.close()

    def get(self, name: str) -> str | None:
        if name in self.r_group:
            return self.r_group[name].uri
        else:
            return None

    def create_array_with_schema(self, name: str, schema: tiledb.ArraySchema) -> str:
        uri = os.path.join(self._uri, name).replace("\\", "/")

        if not tiledb.array_exists(uri, ctx=self.ctx):
            tiledb.Array.create(uri, schema, ctx=self.ctx)
        else:
            # The array exists, but it's not added as group member with the given name.
            # It is possible though that it was added as an anonymous member.
            # In this case we should remove the member, using as key either the uri
            # (if added with relative=False) or the name (if added with relative=True).
            for ref in uri, name:
                try:
                    self.w_group.remove(ref)
                except tiledb.TileDBError:
                    pass
                else:
                    # Attempting to remove and then re-add a member with the same name
                    # fails with "[TileDB::Group] Error: Cannot add group member,
                    # member already set for removal.". To work around this we need to
                    # close the write group (to flush the removal) and and reopen it
                    # (to allow the add operation)
                    self.w_group.close()
                    self.w_group.open("w")

        # register the uri with the given name
        if name not in self.w_group:
            if self._is_cloud:
                self.w_group.add(uri, name, relative=False)
            else:
                self.w_group.add(name, name, relative=True)
        return uri

    def get_or_create_array(self, array_name: str, schema_factory: Callable[[], tiledb.ArraySchema]):
        # get or create TileDB array uri
        uri = self.get(array_name)
        if uri is None:
            schema = schema_factory()
            uri = self.create_array_with_schema(array_name, schema)
        return uri

    def __repr__(self) -> str:
        return f"{type(self).__name__}({self._uri})"
