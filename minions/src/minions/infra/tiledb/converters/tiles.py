from __future__ import annotations

import itertools
import math
from typing import TYPE_CHECKING

import numpy as np

from minions.infra.tiledb.axes import Axes
from minions.infra.tiledb.readers.base import AxesData

if TYPE_CHECKING:
    from collections.abc import Iterator

    import tiledb


def iter_channles(domain: tiledb.Domain) -> Iterator[tuple[slice, ...]]:
    """Generate all the channels that cover the given TileDB domain."""
    channel_size = domain.dim(Axes.C).size if domain.has_dim(Axes.C) else 1
    for channel_idx in range(channel_size):
        channel_slice = slice(channel_idx, channel_idx + 1)
        slices = tuple(
            slice(None) if dim.name != Axes.C else channel_slice for dim in domain
        )
        yield slices


def iter_tiles_from_domain(
    domain: tiledb.Domain,
    tiles_in_axes: AxesData[int] | None = None,
) -> Iterator[tuple[slice, ...]]:
    """Generate all the non-overlapping tiles that cover the given TileDB domain."""
    tiles_in_axes_map = {}
    if tiles_in_axes is not None:
        tiles_in_axes_map = tiles_in_axes.raw
    tiles = [tiles_in_axes_map.get(dim.name, int(dim.tile)) for dim in domain]

    return itertools.product(*map(iter_slices, map(dim_range, domain, tiles)))


def num_tiles(domain: tiledb.Domain) -> int:
    n = 1
    for dim in domain:
        n *= len(dim_range(dim))
    return n


def dim_range(dim: tiledb.Dim, tile: int | None = None) -> range:
    tile = int(tile or dim.tile)
    return range(int(dim.domain[0]), int(dim.domain[1]) + 1, tile)


def iter_slices(r: range) -> Iterator[slice]:
    """Generate all the non-overlapping slices that cover the given range `r` with each slice having length `r.step` (except possibly the last one).

    slice(r[0], r[1])
    slice(r[1], r[2])
    ...
    slice(r[n-2], r[n-1])
    slice(r[n-1], r.stop)
    """  # noqa: E501
    yield from itertools.starmap(slice, itertools.pairwise(r))
    yield slice(r[-1], r.stop)


def clamp_query_shape(
    query_shape: AxesData[int],
    src_dtype: np.dtype,
    clamp_mem_bytes: int,
    clamp_axes: Axes,
    align: int = 512,
):
    item_size = np.dtype(src_dtype).itemsize
    copied_shape = query_shape.raw.copy()
    shape_prod = np.prod(query_shape.data)

    x_shape = copied_shape.get(Axes.X, 1)
    y_shape = copied_shape.get(Axes.Y, 1)

    if clamp_axes == Axes("XY"):
        shape_prod_other_clamp_axes = shape_prod / (x_shape * y_shape)
        clamp_mem_in_axes = ceil_divide(clamp_mem_bytes, shape_prod_other_clamp_axes)
        xy_src_mem = x_shape * y_shape * item_size
        factor = math.sqrt(xy_src_mem / clamp_mem_in_axes)
        factor = max(factor, 1)  # we still need to do align

        copied_shape[Axes.X] = ceil_divide(x_shape, factor * align) * align
        copied_shape[Axes.Y] = ceil_divide(y_shape, factor * align) * align
    elif clamp_axes == Axes("X"):
        shape_prod_other_clamp_axes = shape_prod / x_shape
        clamp_mem_in_axes = ceil_divide(clamp_mem_bytes, shape_prod_other_clamp_axes)
        x_src_mem = x_shape * item_size
        factor = x_src_mem / clamp_mem_in_axes
        factor = max(factor, 1)  # we still need to do align

        copied_shape[Axes.X] = ceil_divide(x_shape, factor * align) * align
    elif clamp_axes == Axes("Y"):
        shape_prod_other_clamp_axes = shape_prod / y_shape
        clamp_mem_in_axes = ceil_divide(clamp_mem_bytes, shape_prod_other_clamp_axes)
        y_src_mem = y_shape * item_size
        factor = y_src_mem / clamp_mem_in_axes
        factor = max(factor, 1)  # we still need to do align

        copied_shape[Axes.Y] = ceil_divide(y_shape, factor * align) * align
    else:
        msg = f"Invalid clamp_axes: {clamp_axes}, only support x,y,or xy"
        raise ValueError(msg)

    query_shape = AxesData[int].from_dict(copied_shape)
    return query_shape


def iter_slices_by_tile_shape(query_range: AxesData[range], tile_shape: AxesData[int]):
    def tile_range():
        for dim in query_range.axes.dims:
            dim_range = query_range.raw[dim]
            start = dim_range.start
            stop = dim_range.stop
            tile = tile_shape.raw.get(dim, stop)
            yield range(start, stop, tile)

    return itertools.product(*map(iter_slices, tile_range()))


def iter_slices_by_clamp(  # noqa: PLR0913
    img_shape: AxesData[int],
    img_dtype: np.dtype,
    clamp_mem_bytes: int,
    query_slice: AxesData[slice] | None = None,
    fixed_query_shape: AxesData[int] | None = None,
    clamp_axes: Axes | None = None,
    align: int = 512,
):
    clamp_axes = clamp_axes or Axes("XY")
    query_range, query_shape = get_query_info(img_shape, query_slice, fixed_query_shape)
    clamped_query_shape = clamp_query_shape(
        query_shape,
        img_dtype,
        clamp_mem_bytes,
        clamp_axes,
        align,
    )
    return iter_slices_by_tile_shape(query_range, clamped_query_shape)


def get_query_info(
    img_shape: AxesData[int],
    query_slice: AxesData[slice] | None = None,
    fixed_query_shape: AxesData[int] | None = None,
):
    query_slice_raw = query_slice.raw if query_slice is not None else {}
    fixed_query_shape_raw = (
        fixed_query_shape.raw if fixed_query_shape is not None else {}
    )
    query_range = {}
    query_shape = {}
    for dim in img_shape.axes.dims:
        stop = img_shape.raw[dim]
        dim_slice = query_slice_raw.get(dim) or slice(None)
        start = dim_slice.start or 0
        stop = dim_slice.stop or stop
        query_range[dim] = range(start, stop)
        shape = stop - start
        query_shape[dim] = fixed_query_shape_raw.get(dim, shape)
    return AxesData[range].from_dict(query_range), AxesData[int].from_dict(query_shape)


def ceil_divide(x: float | np.floating, y: float | np.floating):
    return int(-(x // -y))


def get_chunk_size(cores: int, axes: Axes, shape: tuple[int], align: int = 512):
    chunk_factor = math.ceil(math.sqrt(cores))
    src_x_axis = axes.dims.index(Axes.X)
    src_y_axis = axes.dims.index(Axes.Y)
    chunk_size = list(shape)
    chunk_size[src_x_axis] = (
        ceil_divide(chunk_size[src_x_axis], chunk_factor * align) * align
    )
    chunk_size[src_y_axis] = (
        ceil_divide(chunk_size[src_x_axis], chunk_factor * align) * align
    )
    return chunk_size
