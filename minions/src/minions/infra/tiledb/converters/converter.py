from __future__ import annotations

import math
from collections.abc import Iterator, Mapping, Sequence
from concurrent.futures import ThreadPoolExecutor
from typing import (
    TYPE_CHECKING,
    Any,
)

import numpy as np
import tiledb
from loguru import logger
from tiledb.libtiledb import WebpInputFormat

from minions.domain.common.error import DomainError, DomainErrorCode
from minions.domain.common.utils import unixtime_now_in_ms
from minions.infra.tiledb.axes import Axes
from minions.infra.tiledb.converters import FMT_VERSION
from minions.infra.tiledb.converters.group import ReadWriteGroup
from minions.infra.tiledb.converters.scaler import Scaler
from minions.infra.tiledb.converters.tiles import (
    iter_channles,
    iter_tiles_from_domain,
)
from minions.infra.tiledb.readers.base import (
    AxesData,
    GroupMeta,
    LevelMeta,
    LevelsMeta,
)

if TYPE_CHECKING:
    from minions.infra.tiledb.readers.base import ImageReader


# setting a tile to "infinite" effectively makes it equal to the dimension size
_DEFAULT_TILES = AxesData[int].from_dict(
    {Axes.T: 1, Axes.C: 1, Axes.Z: 1, Axes.Y: 512, Axes.X: 512},
)


def validate(reader: ImageReader, level_min: int):
    source_axes_shape = reader.level_shape(level=level_min)
    for axis in [Axes.T, Axes.Z]:
        axis_dim = source_axes_shape.raw.get(axis)
        if axis_dim and axis_dim > 1:
            raise DomainError(DomainErrorCode.axes_not_support, **{axis: axis_dim})


def to_tiledb(
    reader: ImageReader,
    output_path: str,
    attr_name: str,
    *,
    level_min: int = 0,
    tiles: AxesData[int] | None = None,
    preserve_axes: bool = False,
    chunked: bool = False,
    max_workers: int | None = None,
    exclude_metadata: bool = False,
    compressor: Mapping[int, Any] | Any | None = None,
    pyramid_kwargs: Mapping[str, Any] | None = None,
    config: tiledb.Config | None = None,
):
    """Convert an image to a TileDB Group of Arrays, one per level.

    :param reader: ImageReader object
    :param output_path: path to the TileDB group of arrays
    :param level_min: minimum level of the image to be converted. By default set to 0
        to convert all levels.
    :param tiles: A mapping from dimension name (one of 'T', 'C', 'Z', 'Y', 'X') to
        the (maximum) tile for this dimension.
    :param preserve_axes: If true, preserve the axes order of the original image.
    :param chunked: If true, convert one tile at a time instead of the whole image.
        **Note**: The OpenSlideConverter may not be 100% lossless with chunked=True
        for levels>0, even though the converted images look visually identical to the
        original ones.
    :param max_workers: Maximum number of threads that can be used for conversion.
        Applicable only if chunked=True. None means use ThreadPool default.
    :param exclude_metadata: If true, drop original metadata of the images
        and exclude them from being ingested.
    :param compressor: TileDB compression filter mapping for each level
    :param log: verbose logging, defaults to None.
        Allows passing custom logging.Logger or boolean.
        If None or bool=False it initiates an INFO level logging.
        If bool=True then a logger is instantiated in
        DEBUG logging level.
    :param reader_kwargs: Keyword arguments passed to the _ImageReaderType constructor.
    :param pyramid_kwargs: Keyword arguments passed to the scaler constructor for
        generating downsampled versions of the base level. Valid keyword arguments are:
        scale_factors (Required): The downsampling factor for each level
        chunked (Optional): Default False. If true the image is split into chunks and
            each one is independently downsampled. If false the entire image is
            downsampled at once, but it requires more memory.
        progressive (Optional): Default False. If true each downsampled image is
            generated using the previous level. If false for every downsampled image
            the level_min is used, but it requires more memory.
        max_workers (Optional): Default None. The maximum number of workers for
            chunked downsampling. If None, it will default to ProcessPool's default
            which is the number of processors on the machine .
    """
    logger.info(
        "using: tiledb={}, libtiledb={}",
        tiledb.version(),
        tiledb.libtiledb.version(),
    )

    validate(reader, level_min)

    max_tiles = _DEFAULT_TILES
    if tiles:
        copied_tiles = _DEFAULT_TILES.raw.copy()
        copied_tiles.update(tiles.raw)
        max_tiles = AxesData[int].from_dict(data=copied_tiles)
    logger.debug(f"max tiles in each dim:{max_tiles}")

    rw_group = ReadWriteGroup(output_path, config=config)

    image_metadata = {}
    original_metadata = None

    with rw_group:
        stored_fmt_version = rw_group.r_group.meta.get("fmt_version")
        if stored_fmt_version not in (None, FMT_VERSION):
            logger.warning(
                "incremental ingestion is not supported for different versions: "
                f"current version is {FMT_VERSION}, stored version is {stored_fmt_version} - "
                f"default Fallback: No changes will apply to already ingested image",
            )
            return

        # Check if compressor Mapping has 1-1 correspondance
        level_count = reader.level_count()
        if isinstance(compressor, Mapping) and len(compressor.items()) != level_count:
            msg = f"compressor filter mapping does not map every level to a Filter {len(compressor.items())} != {level_count}"
            raise ValueError(msg)

        compressors = {}
        for pyramid_level in range(level_min, level_count):
            if compressor is None:
                compressors[pyramid_level] = tiledb.ZstdFilter()
            elif isinstance(compressor, tiledb.Filter):
                if isinstance(compressor, tiledb.WebpFilter):
                    msg = "WebpFilter not support yet"
                    raise ValueError(msg)
                    if compressor.input_format == WebpInputFormat.WEBP_NONE:
                        compressor = tiledb.WebpFilter(
                            input_format=reader.webp_format(),
                            quality=compressor.quality,
                            lossless=compressor.lossless,
                        )
                # One filter is given apply to all levels
                compressors[pyramid_level] = compressor
            elif isinstance(compressor, Mapping):
                compressors = compressor  # type: ignore
                break

        convert_kwargs = {
            "reader": reader,
            "rw_group": rw_group,
            "max_tiles": max_tiles,
            "preserve_axes": preserve_axes,
            "chunked": chunked,
            "max_workers": max_workers,
            "compressor": compressors,
            "attr_name": attr_name,
        }
        logger.debug(f"convert arguments : {convert_kwargs}")

        base_level_array_uri = _convert_level_to_tiledb(level_min, **convert_kwargs)

        if pyramid_kwargs is not None:
            logger.debug(
                "use level {} to create pyramid with {}",
                level_min,
                pyramid_kwargs,
            )

            _create_image_pyramid(
                rw_group,
                pyramid_base_uri=base_level_array_uri,
                base_level=level_min,
                max_tiles=max_tiles,
                compressor=compressors,
                attr_name=attr_name,
                **pyramid_kwargs,
            )

        else:
            for pyramid_level in range(level_min + 1, level_count):
                _convert_level_to_tiledb(pyramid_level, **convert_kwargs)

        if not exclude_metadata:
            original_metadata = reader.original_metadata()

    # we need another with here,
    # as to reflush and get latest data from r_group read-mode
    with rw_group:
        image_metadata = reader.image_metadata()
        levels_meta = LevelsMeta(levels=list(_iter_levels_meta(rw_group.r_group, tdb_ctx=rw_group.ctx)))
        min_level_meta = min(levels_meta.levels, key=lambda x: x.level)
        group_meta = GroupMeta(
            axes=min_level_meta.axes,
            fmt_version=FMT_VERSION,
            levels_metadata=levels_meta,
            image_metadata=image_metadata,
            original_metadata=original_metadata,
            data_version=str(unixtime_now_in_ms()),
        )
        logger.debug("ingested group meta {}", group_meta)
        rw_group.w_group.meta.update(group_meta.to_tdb())


def generate_convert_kwargs(reader: ImageReader) -> dict[str, Any]:
    args = {
        # only set true if the base level image is too large to process
        "chunked": False,
        # only set if source without a pyramid
        "pyramid_kwargs": None,
    }

    pyramid_kwargs = {}

    base_level = 0

    base_shape_with_axes = reader.level_shape(base_level)
    base_dtype = reader.level_dtype(base_level)
    yx_axes = Axes("YX")
    base_yx = base_shape_with_axes.map_to_axes(yx_axes)
    # use 512 as the default width or height fit size,
    # this means we build pyrimid to this size
    fit_size = 512
    pyrimid_level_count = math.ceil(math.log2(max(base_yx) / fit_size))

    use_image_levels = reader.level_count() >= pyrimid_level_count + 1

    scale_factors = []
    pyrimid_shape = np.asarray(base_yx)
    for pyrimid_level in range(1, pyrimid_level_count + 1):
        scale_factors.append(2**pyrimid_level)

        if use_image_levels:
            level_shape = reader.level_shape(pyrimid_level)
            pyrimid_shape = np.rint(pyrimid_shape / 2).astype(np.int32)

            use_image_levels = np.array_equal(
                level_shape.map_to_axes(yx_axes),
                pyrimid_shape,
            )

    pyramid_kwargs["scale_factors"] = scale_factors

    logger.debug(
        "generate convert kwargs for image {base_shape}-{base_dtype} and {pyramid_info}",
        base_shape=base_shape_with_axes,
        base_dtype=base_dtype,
        pyramid_info={
            "create-pyramid-manually": not use_image_levels,
            "pyramid-info": pyramid_kwargs,
        },
    )

    if not use_image_levels:
        args["pyramid_kwargs"] = pyramid_kwargs

    return args


def _iter_levels_meta(group: tiledb.Group, tdb_ctx: tiledb.Ctx) -> Iterator[LevelMeta]:
    for o in group:
        with tiledb.open(o.uri, ctx=tdb_ctx) as array:
            level = array.meta["level"]
            domain = array.schema.domain
            axes = "".join(dim.name for dim in domain)
            meta = LevelMeta(level=level, axes=axes, shape=array.shape, name=o.name)
            yield meta


def _convert_level_to_tiledb(  # noqa: PLR0913
    level: int,
    *,
    reader: ImageReader,
    rw_group: ReadWriteGroup,
    max_tiles: AxesData[int],
    preserve_axes: bool,
    chunked: bool,
    max_workers: int | None,
    compressor: Mapping[int, tiledb.Filter],
    attr_name: str,
):
    logger.debug("ingest level {}", level)

    source_axes_shape = reader.level_shape(level)
    # cause shape here should be same for src and tgt, so we can just resort the axes
    target_axes_shape = source_axes_shape if preserve_axes else source_axes_shape.canonicalize(lambda x: x[1] > 1)

    attr_dtype = reader.level_dtype(level)
    level_array_uri = _create_level_array_schema_in_group(
        rw_group,
        level,
        target_axes_shape,
        max_tiles,
        attr_dtype,
        compressor.get(level, tiledb.ZstdFilter()),
        attr_name=attr_name,
    )

    # write image and metadata to TileDB array
    with tiledb.open(
        level_array_uri,
        "w",
        attr=attr_name,
        ctx=rw_group.ctx,
    ) as tdb_level_array:
        # we need this level info as we need retrieve it when iter levels
        tdb_level_array.meta.update(level=level)
        source_to_target = source_axes_shape.axes.mapper(target_axes_shape.axes)
        target_to_source = source_to_target.inverse

        if chunked:

            def tile_to_tiledb(
                tile_slices: tuple[slice, ...],
            ):
                source_tile = target_to_source.map_tile(tile_slices)
                source_img_array = reader.level_image(level, source_tile)
                tdb_level_array[tile_slices] = source_to_target.map_array(
                    source_img_array,
                )

            ex = ThreadPoolExecutor(max_workers) if max_workers != 0 else None
            mapper = getattr(ex, "map", map)

            # just iterate it without considering the return value
            list(mapper(tile_to_tiledb, iter_tiles_from_domain(tdb_level_array.domain)))

            if ex:
                ex.shutdown()
        else:
            for idx, channel_slices in enumerate(iter_channles(tdb_level_array.domain)):
                logger.debug("ingest channel {}", idx)
                source_slices = target_to_source.map_tile(channel_slices)
                source_img_array = reader.level_image(level, source_slices)
                logger.debug("data read")
                tdb_level_array[channel_slices] = source_to_target.map_array(
                    source_img_array,
                )
                logger.debug("data write")

        # logger.debug("start to consolidate")
        # tdb_level_array.consolidate()
        # logger.debug("consolidate done")
        return level_array_uri


def _create_image_pyramid(  # noqa: PLR0913
    rw_group: ReadWriteGroup,
    pyramid_base_uri: str,
    base_level: int,
    max_tiles: AxesData[int],
    compressor: Mapping[int, tiledb.Filter],
    attr_name: str,
    scale_factors: Sequence[float],
    *,
    progressive: bool = True,
    max_workers: int | None = None,
):
    scaler = Scaler(max_workers=max_workers)

    previous_scale_factor = 1.0
    for idx, scale_factor in enumerate(scale_factors):
        pyramid_level = base_level + 1 + idx
        logger.debug("create pyramid {} with factor {}", pyramid_level, scale_factor)

        with tiledb.open(
            pyramid_base_uri,
            attr=attr_name,
            ctx=rw_group.ctx,
        ) as tdb_pyramid_base_array:
            calulated_scale_factor = scale_factor
            if progressive:
                calulated_scale_factor = scale_factor / previous_scale_factor

            base_array_dtype = tdb_pyramid_base_array.dtype

            pyramid_axes_shape = _get_scaled_axes_shape_for_domain(
                tdb_pyramid_base_array.domain,
                calulated_scale_factor,
            )
            level_array_uri = _create_level_array_schema_in_group(
                rw_group,
                pyramid_level,
                pyramid_axes_shape,
                max_tiles,
                base_array_dtype,
                compressor.get(pyramid_level, tiledb.ZstdFilter()),
                attr_name=attr_name,
            )

            with tiledb.open(
                level_array_uri,
                mode="w",
                attr=attr_name,
                ctx=rw_group.ctx,
            ) as tdb_level_array:
                tdb_level_array.meta.update(level=pyramid_level)
                scaler.apply(tdb_pyramid_base_array, tdb_level_array)
                tdb_level_array.consolidate()

        if progressive:
            previous_scale_factor = scale_factor
            pyramid_base_uri = level_array_uri


def _get_scaled_axes_shape_for_domain(
    domain: tiledb.Domain,
    scale_factor,
) -> AxesData[int]:
    def get_kv():
        for dim in domain:
            dim_name = dim.name
            dim_shape = int(dim.domain[1]) - int(dim.domain[0]) + 1
            scaled_shape = dim_shape if dim_name not in SUPPORTED_SCALE_AXES.dims else round(dim_shape / scale_factor)
            yield dim_name, scaled_shape

    return AxesData[int].from_dict(dict(get_kv()))


def _get_pixel_depth_by_filter(compressor: tiledb.Filter) -> int:
    if not isinstance(compressor, tiledb.WebpFilter):
        return 1
    webp_format = compressor.input_format
    if webp_format in (WebpInputFormat.WEBP_RGB, WebpInputFormat.WEBP_BGR):
        return 3
    if webp_format in (WebpInputFormat.WEBP_RGBA, WebpInputFormat.WEBP_BGRA):
        return 4
    msg = f"Invalid WebpInputFormat: {compressor.input_format}"
    raise ValueError(msg)


def _create_level_array_schema_in_group(  # noqa: PLR0913
    rw_group: ReadWriteGroup,
    level: int,
    axes_shape: AxesData[int],
    max_tiles: AxesData[int],
    attr_dtype: np.dtype,
    compressor: tiledb.Filter,
    attr_name: str,
):
    # get or create TileDB array uri
    array_name = f"l_{level}.tdb"

    def schema_factory():
        return _generate_tdb_schema(
            axes_shape,
            max_tiles,
            attr_dtype,
            compressor,
            attr_name,
        )

    if rw_group.get(array_name) is not None:
        logger.warning(
            f"{array_name} exist, try remove first if you need to override schema",
        )
    uri = rw_group.get_or_create_array(array_name, schema_factory)
    return uri


def _generate_tdb_schema(
    axes_shape: AxesData[int],
    max_tiles: AxesData[int],
    attr_dtype: np.dtype,
    compressor: tiledb.Filter,
    attr_name: str,
) -> tiledb.ArraySchema:
    # All dimensions must have the same dtype
    dim_dtype = np.dtype(np.uint32)

    dim_compressor = compressor
    if isinstance(compressor, tiledb.WebpFilter):
        # WEBP Compressor does not accept
        # specific dtypes so for dimensions we use the default
        dim_compressor = tiledb.ZstdFilter()

    dims = []
    # dim order here has impact on performance: https://docs.tiledb.com/main/how-to/arrays/creating-arrays/creating-the-array-domain
    # The order of the dimensions as added to the domain
    # is important later when slicing subarrays.
    # Remember to give priority to more selective dimensions,
    # in order to maximize the pruning power during slicing.
    for dim_name in axes_shape.axes.dims:
        dim_size = axes_shape.raw[dim_name]
        dim_tile = min(dim_size, max_tiles.raw[dim_name])
        dim = tiledb.Dim(
            dim_name,
            (0, dim_size - 1),
            dim_tile,
            dtype=dim_dtype,
            filters=[dim_compressor],
        )
        dims.append(dim)

    attr = tiledb.Attr(name=attr_name, dtype=attr_dtype, filters=[compressor])
    return tiledb.ArraySchema(domain=tiledb.Domain(*dims), attrs=[attr])


SUPPORTED_SCALE_AXES = Axes([Axes.X, Axes.Y])
"""
only support XY for now, as opencv resize only do with XY
"""
