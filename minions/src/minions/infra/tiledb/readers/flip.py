from minions.infra.tiledb.axes import Axes
from minions.infra.tiledb.readers.base import <PERSON><PERSON><PERSON><PERSON><PERSON>, ComposeReader, ImageReader

SUPPORTED_FLIP_AXES = [Axes.X, Axes.Y]


class FlipReader(ComposeReader):
    def __init__(self, src_reader: ImageReader, flip_axes: Axes):
        super().__init__(src_reader)
        self.flip_axes = flip_axes

    def level_image(self, level: int, slices: tuple[slice, ...]):
        flipped_arr = self.src_reader.level_image(level, slices)
        return flipped_arr
