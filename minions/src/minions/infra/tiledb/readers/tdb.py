import fast_histogram
import numpy as np
import tiledb
from loguru import logger

from minions.domain.common.error import DomainError, DomainErrorCode
from minions.domain.common.utils import unixtime_now_in_ms
from minions.infra.tiledb.axes import Axes
from minions.infra.tiledb.readers.base import (
    ArrayData,
    ArraySelector,
    AxesData,
    GroupMeta,
    Histogram,
    ImageMeta,
    ImageReader,
    OriginalMeta,
)

ATTR_INTENSITY = "intensity"


class TdbReader(ImageReader):
    def __init__(
        self,
        group_uri: str,
        config: tiledb.Config | None = None,
    ) -> None:
        self._group_uri = group_uri
        self.ctx = tiledb.Ctx(config)
        self.tdb_levels: dict[int | None, tiledb.DenseArray | tiledb.SparseArray] = {}

    def load_group_meta(self):
        try:
            self._group = tiledb.Group(self._group_uri, ctx=self.ctx)
            self._group_meta = GroupMeta.from_tdb(self._group.meta)
        except (tiledb.TileDBError, TypeError) as e:
            logger.exception("open group {} failed", self._group_uri)
            raise DomainError(DomainErrorCode.missing_group, uri=self._group_uri) from e

    def __enter__(self):
        self.preload()
        return self

    def __exit__(self, *args, **kwargs):
        self.close()

    def preload(self, timestamp: int | None = None):
        self.load_group_meta()
        levels_meta = self.group_meta.levels_metadata.levels
        none_level = None
        # this is for None level which will use the base one
        base_tdb_uri = self.find_level_uri(none_level)
        for level_meta in levels_meta:
            level = level_meta.level
            tdb = self.tdb_levels.get(level)
            if tdb is None or timestamp is not None:
                tdb_uri = self.find_level_uri(level)
                tdb = self.open_intensity(tdb_uri, timestamp=timestamp)
                if tdb_uri == base_tdb_uri:
                    self.tdb_levels[none_level] = tdb
                self.tdb_levels[level] = tdb
            else:
                tdb.reopen()

    def close(self):
        self._group.close()
        for tdb in self.tdb_levels.values():
            tdb.close()
        self.tdb_levels = {}

    def get_level_arr(self, level: int | None = None):
        tdb = self.tdb_levels.get(level)
        if tdb is None:
            raise DomainError(
                DomainErrorCode.missing_level_in_cache,
                level=level,
                reason="try preload reader first",
            )

        return tdb

    def find_level_uri(self, level: int | None) -> str:
        levels_meta = self._group_meta.levels_metadata.levels
        level_name = None
        if level is None:
            if len(levels_meta) < 1:
                raise DomainError(DomainErrorCode.missing_level, level=level)
            # the one with minimal level, should be the largest resolution,
            # but it may not at the level 0,
            # so we can't assume level is 0 when passing is None
            level_name = min(levels_meta, key=lambda x: x.level).name

        for x in levels_meta:
            if x.level == level:
                level_name = x.name

        for level_obj in self._group:
            if level_obj.name == level_name:
                return level_obj.uri

        raise DomainError(DomainErrorCode.missing_level, level=level)

    def axes(self) -> Axes:
        return Axes(self._group_meta.axes)

    def level_count(self) -> int:
        return len(self._group_meta.levels_metadata.levels)

    def level_dtype(self, level: int | None = None) -> np.dtype:
        return self.get_level_arr(level).dtype

    def level_shape(self, level: int | None = None):
        tdb = self.get_level_arr(level)
        shape_with_axes = AxesData[int].from_tuple(self.axes(), tdb.shape)
        return shape_with_axes

    def level_image(
        self,
        level: int | None = None,
        slices: tuple[slice, ...] | None = None,
    ) -> np.ndarray:
        tdb = self.get_level_arr(level)
        logger.debug(
            "read {file} with {domain} by {slices}",
            file=tdb.uri,
            slices=slices,
            domain={dim.name: dim.domain for dim in tdb.domain},
        )
        data_in_db = tdb[slices] if slices else tdb[:]
        return data_in_db

    def image_metadata(self) -> ImageMeta:
        return self._group_meta.image_metadata

    def original_metadata(self) -> OriginalMeta | None:
        return self._group_meta.original_metadata

    def open_intensity(self, uri: str, mode: str = "r", timestamp: int | None = None):
        return tiledb.open(
            uri,
            mode,
            attr=ATTR_INTENSITY,
            timestamp=timestamp,
            ctx=self.ctx,
        )

    def open_level_for_write(self, level: int | None = None):
        level_uri = self.find_level_uri(level)
        return self.open_intensity(level_uri, mode="w")

    def query(self, array_selector: ArraySelector | None = None) -> ArrayData:
        array_selector = array_selector or ArraySelector()
        axes_in_db = self.axes()
        data = self.level_image(
            array_selector.level,
            array_selector.slices and array_selector.slices.map_to_axes(axes_in_db),
        )
        return ArrayData(axes=axes_in_db, data=data)

    def query_agg(self, array_selector: ArraySelector, aggs) -> dict[str, float]:
        axes_in_db = self.axes()
        slices = slice(None) if array_selector.slices is None else array_selector.slices.map_to_axes(axes_in_db)
        tdb_arr = self.get_level_arr(array_selector.level)
        q = tdb_arr.query()
        agg_res = q.agg(aggs)[slices]
        return agg_res

    def update_channel_min_max_meta(self, channel_ids: list[int] | None = None):
        logger.debug("start to update channel min/max meta")

        meta = self.group_meta
        image_meta = meta.image_metadata
        for channel in image_meta.Channels:
            if channel_ids is not None and channel.id not in channel_ids:
                continue

            channel_slice = slice(channel.id, channel.id + 1)
            select_one_ch = ArraySelector(
                slices=AxesData[slice].from_dict(
                    {
                        Axes.C: channel_slice,
                    },
                ),
            )
            agg = self.query_agg(array_selector=select_one_ch, aggs=["min", "max"])
            min_agg = agg.get("min")
            max_agg = agg.get("max")
            if min_agg is not None:
                channel.min = min_agg
            if max_agg is not None:
                channel.max = max_agg
        image_meta_kv = meta.get_image_metadata_update_kv()
        self.update_meta(image_meta_kv, update_data_version=True)

        logger.debug("update channel min/max meta done")

        self.update_histogram(channel_ids=channel_ids)

    def update_histogram(self, channel_ids: list[int] | None = None):
        logger.debug("start to update histogram")
        bin_size = 256

        meta = self.group_meta
        img_meta = meta.image_metadata

        tdb_axes = self.axes()
        tdb_arr = self.get_level_arr()
        for channel in img_meta.Channels:
            if channel_ids is not None and channel.id not in channel_ids:
                continue

            ch_query_slice = (
                AxesData[slice]
                .from_dict(
                    {
                        Axes.C: slice(channel.id, channel.id + 1),
                    },
                )
                .map_to_axes(tdb_axes)
            )

            ch_arr = tdb_arr[ch_query_slice]

            if channel.min is None or channel.max is None:
                channel.min = float(ch_arr.min())
                channel.max = float(ch_arr.max())

            channel_range = [channel.min, channel.max]
            bins = np.linspace(channel.min, channel.max, num=(bin_size + 1))
            hs = fast_histogram.histogram1d(ch_arr, bins=bin_size, range=channel_range)
            channel.histogram = Histogram(hist=hs.tolist(), bins=bins.tolist())

        img_meta_kv = meta.get_image_metadata_update_kv()
        self.update_meta(img_meta_kv)
        logger.debug("update histogram done")

    @property
    def group_meta(self) -> GroupMeta:
        return self._group_meta

    @property
    def sorted_level_metas(self):
        return sorted(
            self.group_meta.levels_metadata.levels,
            key=lambda x: x.level,
        )

    def update_meta(self, meta: dict, *, update_data_version=False):
        with tiledb.Group(self._group.uri, mode="w", ctx=self.ctx) as group:
            if update_data_version:
                meta["data_version"] = str(unixtime_now_in_ms())
            group.meta.update(meta)

        # reload to make it alive for later usage
        self.load_group_meta()

    def consolidate(self, *, vacuum: bool, config: dict | None = None):
        logger.debug("start to consolidate with vacuum {} and {}", vacuum, config)
        tdb_config = None if config is None else tiledb.Config(config)
        for arr in self._group:
            tiledb.consolidate(arr.uri, config=tdb_config, ctx=self.ctx)
            if vacuum:
                tiledb.vacuum(arr.uri, config=tdb_config, ctx=self.ctx)

        # as we consolidate info, need to reopen
        self.preload()
        logger.debug("consolidate done")


COLOR_PRESETS = [
    "#0000ff",
    "#ff0000",
    "#00ff00",
    "#00ffff",
    "#ff00ff",
    "#ffff00",
    "#ffffff",
    "#007d7d",
    "#7d007d",
    "#7d7d00",
    "#fff1f0",
    "#ffccc7",
    "#ffa39e",
    "#ff7875",
    "#ff4d4f",
    "#f5222d",
    "#cf1322",
    "#a8071a",
    "#820014",
    "#5c0011",
    "#f6ffed",
    "#d9f7be",
    "#b7eb8f",
    "#95de64",
    "#73d13d",
    "#52c41a",
    "#389e0d",
    "#237804",
    "#135200",
    "#092b00",
    "#e6f4ff",
    "#bae0ff",
    "#91caff",
    "#69b1ff",
    "#4096ff",
    "#1677ff",
    "#0958d9",
    "#003eb3",
    "#002c8c",
    "#001d66",
    "#e6fffb",
    "#b5f5ec",
    "#87e8de",
    "#5cdbd3",
    "#36cfc9",
    "#13c2c2",
    "#08979c",
    "#006d75",
    "#00474f",
    "#002329",
    "#fff0f6",
    "#ffd6e7",
    "#ffadd2",
    "#ff85c0",
    "#f759ab",
    "#eb2f96",
    "#c41d7f",
    "#9e1068",
    "#780650",
    "#520339",
    "#feffe6",
    "#ffffb8",
    "#fffb8f",
    "#fff566",
    "#ffec3d",
    "#fadb14",
    "#d4b106",
    "#ad8b00",
    "#876800",
    "#614700",
    "#fff7e6",
    "#ffe7ba",
    "#ffd591",
    "#ffc069",
    "#ffa940",
    "#fa8c16",
    "#d46b08",
    "#ad4e00",
    "#873800",
    "#612500",
    "#f9f0ff",
    "#efdbff",
    "#d3adf7",
    "#b37feb",
    "#9254de",
    "#722ed1",
    "#531dab",
    "#391085",
    "#22075e",
    "#120338",
]


def get_default_color_by_id(channel_id: int):
    color_hex = COLOR_PRESETS[channel_id % len(COLOR_PRESETS)]
    return color_hex
