from __future__ import annotations

from enum import StrEnum
from typing import TYPE_CHECKING, Annotated

from pydantic import AfterValida<PERSON>, BaseModel

from minions.infra.tiledb.axes import Axes
from minions.infra.tiledb.readers.region import Region, Segment

if TYPE_CHECKING:
    from pathlib import Path


class ChannelArg(BaseModel):
    id: int
    min: float
    max: float


class RescaleIntensityReq(BaseModel):
    tdb_uri: str
    channels: list[ChannelArg]
    preview_path: Path


class ClipIntensityReq(BaseModel):
    tdb_uri: str
    channels: list[ChannelArg]
    preview_path: Path


class UpdateHistogramReq(BaseModel):
    tdb_uri: str
    channel_ids: list[int] | None = None


class GetMinMaxSummaryReq(BaseModel):
    tdb_uri: str
    channel_id: int
    min: float
    max: float


class GetMinMaxByQuantileReq(BaseModel):
    tdb_uri: str
    channel_id: int
    min_quantile: float | None = None
    max_quantile: float | None = None
    in_unique: bool


class GetMinMaxByQuantileRes(BaseModel):
    min: float | None = None
    max: float | None = None


class GetMinMaxSummaryRes(BaseModel):
    below_min_count: int
    above_max_count: int
    below_min_ratio: float
    above_max_ratio: float
    below_min_in_unique_ratio: float
    above_max_in_unique_ratio: float


class BitDepthConvertReq(BaseModel):
    src_img_id: str
    tgt_img_id: str
    tgt_bit_depth: str


def only_one_axis(value: Axes) -> Axes:
    if len(value.dims) != 1:
        msg = f"only support one axis flip for now, but get {value}"
        raise ValueError(msg)
    return value


OneAxes = Annotated[Axes, AfterValidator(only_one_axis)]


class FlipReq(BaseModel):
    tdb_uri: str
    preview_path: Path
    flip_axes: OneAxes


class ChannelOutput(BaseModel):
    sub_path: str
    channel_ids: list[int]


tiff_suffix = ".tiff"
irs_suffix = ".irs"
jpg_suffix = ".jpg"


class SupportedExportType(StrEnum):
    tiff = "tiff"

    def to_suffix(self):
        suffix = ""
        match self:
            case SupportedExportType.tiff:
                suffix = tiff_suffix
        return suffix


class SupportedSaveAsType(StrEnum):
    tiff = "tiff"
    irs = "irs"
    jpg = "jpg"

    def to_suffix(self):
        suffix = ""
        match self:
            case SupportedSaveAsType.tiff:
                suffix = tiff_suffix
            case SupportedSaveAsType.irs:
                suffix = irs_suffix
            case SupportedSaveAsType.jpg:
                suffix = jpg_suffix
        return suffix


class XYRegion(BaseModel):
    x: int
    y: int
    width: int
    height: int

    def get_segments(self):
        x_segment = Segment(start=self.x, length=self.width)
        y_segment = Segment(start=self.y, length=self.height)
        return x_segment, y_segment


class SaveAsReq(BaseModel):
    src_img_id: str
    channel_outputs: list[ChannelOutput]
    tgt_type: SupportedSaveAsType
    region: XYRegion | None = None

    def get_reader_region(self, channel_ids: list[int]):
        if self.region is None:
            return Region(channel_ids=channel_ids)
        else:
            x_seg, y_seg = self.region.get_segments()
            return Region(x_segment=x_seg, y_segment=y_seg, channel_ids=channel_ids)


class MergeChannelsReq(BaseModel):
    output_folder_id: str
    output_img_id: str
    merge_imgs: list[MergeImg]

    class MergeImg(BaseModel):
        id: str
        channels: list[int]
