import asyncio
import os
from datetime import datetime, timedelta

import dask.array as da
import numpy as np
import skimage.exposure as ski
from loguru import logger

from minions.domain.analysis.model import (
    BitDepthConvertReq,
    ClipIntensityReq,
    FlipReq,
    GetMinMaxByQuantileReq,
    GetMinMaxByQuantileRes,
    GetMinMaxSummaryReq,
    GetMinMaxSummaryRes,
    RescaleIntensityReq,
    SaveAsReq,
    SupportedSaveAsType,
    UpdateHistogramReq,
)
from minions.domain.common.error import DomainError, DomainErrorCode
from minions.domain.common.utils import timing
from minions.domain.ingest.svc import IngestSvc
from minions.domain.query.img.svc import ImgSvc
from minions.infra.tiledb.axes import Axes
from minions.infra.tiledb.converters.tiles import (
    get_chunk_size,
    iter_slices_by_clamp,
)
from minions.infra.tiledb.readers.base import ArraySelector, AxesData
from minions.infra.tiledb.readers.bit_depth_convert import BitDepthConvertReader
from minions.infra.tiledb.readers.region import RegionReader
from minions.infra.tiledb.readers.tdb import TdbReader
from minions.infra.tiledb.writers.colored_img import ColoredImgWriter
from minions.infra.tiledb.writers.ome_tiff import OMETiffWriter


class ImageFilterSvc:
    def __init__(self, img_svc: ImgSvc, ingest_svc: IngestSvc):
        self.img_svc = img_svc
        self.ingest_svc = ingest_svc
        self.rescale_clamp_mem = self.get_rescale_clamp_mem()
        self.clip_clamp_mem = self.get_clip_clamp_mem()
        self.flip_clamp_mem = self.get_flip_clamp_mem()

    def get_rescale_clamp_mem(self):
        return 1024**3

    def get_clip_clamp_mem(self):
        return 1024**3

    def get_flip_clamp_mem(self):
        return 1024**3 * 10

    def flip(self, req: FlipReq):
        if not isinstance(req, FlipReq):
            req = FlipReq.model_validate(req)

        with TdbReader(req.tdb_uri) as tdb:
            src_axes = tdb.axes()
            img_shape = tdb.level_shape()
            img_dtype = tdb.level_dtype()
            with tdb.open_level_for_write() as level_tdb:
                for flip_axis in req.flip_axes.dims:
                    flip_axis_idx = src_axes.dims.index(flip_axis)
                    flip_axis_slice = slice(None)
                    query_slice = AxesData[slice].from_dict(
                        {
                            flip_axis: flip_axis_slice,
                        }
                    )

                    for tile_slices in iter_slices_by_clamp(
                        img_shape,
                        img_dtype,
                        self.flip_clamp_mem,
                        query_slice=query_slice,
                    ):
                        query_arr = tdb.level_image(slices=tile_slices)
                        with timing(f"flip for {tile_slices}"):
                            flipped_arr = np.flip(query_arr, axis=flip_axis_idx)
                        level_tdb[tile_slices] = flipped_arr

                    logger.debug("done store for flip => {}", flip_axis)

            # update data version
            tdb.update_meta({}, update_data_version=True)

            tdb.preload()  # as we write data above, so need to refresh reader

            # re-generate-pyrimid here
            tdb.regenerate_image_pyramid()

            with logger.catch(message="generate preview img faild when flip"):
                self.img_svc.generate_preview_img(req.preview_path, tdb)

    def rescale_intensity(self, req: RescaleIntensityReq):
        if not isinstance(req, RescaleIntensityReq):
            req = RescaleIntensityReq.model_validate(req)

        with TdbReader(req.tdb_uri) as tdb:
            img_shape = tdb.level_shape()
            img_dtype = tdb.level_dtype()
            with tdb.open_level_for_write() as level_tdb:
                # regions
                for ch in req.channels:
                    channel_id = ch.id
                    channel_slice = slice(channel_id, channel_id + 1)
                    query_slice = AxesData[slice].from_dict(
                        {
                            Axes.C: channel_slice,
                        }
                    )
                    for tile_slices in iter_slices_by_clamp(
                        img_shape,
                        img_dtype,
                        self.rescale_clamp_mem,
                        query_slice=query_slice,
                    ):
                        channel_arr = tdb.level_image(slices=tile_slices)
                        chunk_size = get_chunk_size(
                            os.cpu_count(),
                            img_shape.axes,
                            channel_arr.shape,
                        )
                        channel_arr = da.from_array(channel_arr, chunks=chunk_size)

                        rescaled_arr = ski.rescale_intensity(
                            channel_arr,
                            in_range=(ch.min, ch.max),
                        )

                        rescaled_arr = rescaled_arr.compute()

                        level_tdb[tile_slices] = rescaled_arr

                    logger.debug("done store for ch => {}", ch.id)

            # update channel meta here
            tdb.preload()  # as we write data above, so need to refresh reader
            tdb.update_channel_min_max_meta(channel_ids=[ch.id for ch in req.channels])

            # re-generate-pyrimid here
            tdb.regenerate_image_pyramid()

            with logger.catch(message="generate preview img faild when rescale intensity"):
                self.img_svc.generate_preview_img(req.preview_path, tdb)

    def clip_intensity(self, req: ClipIntensityReq):
        if not isinstance(req, ClipIntensityReq):
            req = ClipIntensityReq.model_validate(req)

        with TdbReader(req.tdb_uri) as tdb:
            img_shape = tdb.level_shape()
            img_dtype = tdb.level_dtype()
            with tdb.open_level_for_write() as level_tdb:
                for ch in req.channels:
                    channel_id = ch.id
                    channel_slice = slice(channel_id, channel_id + 1)
                    query_slice = AxesData[slice].from_dict(
                        {
                            Axes.C: channel_slice,
                        }
                    )
                    for tile_slices in iter_slices_by_clamp(
                        img_shape,
                        img_dtype,
                        self.clip_clamp_mem,
                        query_slice=query_slice,
                    ):
                        channel_arr = tdb.level_image(slices=tile_slices)
                        with timing(f"clip for {tile_slices}"):
                            clipped_arr = np.clip(channel_arr, ch.min, ch.max)
                        level_tdb[tile_slices] = clipped_arr

                    logger.debug("done store for ch => {}", ch.id)

            # update channel meta here
            tdb.preload()  # as we write data above, so need to refresh reader
            tdb.update_channel_min_max_meta(channel_ids=[ch.id for ch in req.channels])

            # re-generate-pyrimid here
            tdb.regenerate_image_pyramid()

            with logger.catch(message="generate preview img faild when clip intensity"):
                self.img_svc.generate_preview_img(req.preview_path, tdb)

    def update_histogram(self, req: UpdateHistogramReq):
        if not isinstance(req, UpdateHistogramReq):
            req = UpdateHistogramReq.model_validate(req)

        with TdbReader(req.tdb_uri) as tdb:
            tdb.update_histogram(req.channel_ids)

    def get_min_max_summary(self, req: GetMinMaxSummaryReq):
        if not isinstance(req, GetMinMaxSummaryReq):
            req = GetMinMaxSummaryReq.model_validate(req)

        with TdbReader(req.tdb_uri) as tdb:
            channel_id = req.channel_id
            channel_slice = slice(channel_id, channel_id + 1)
            select_one_ch = ArraySelector(
                slices=AxesData[slice].from_dict(
                    {
                        Axes.C: channel_slice,
                    }
                )
            )
            channel_data = da.from_array(tdb.query(array_selector=select_one_ch).data)

            logger.debug("start below min")
            # Calculate counts for min/max thresholds
            below_min = (channel_data <= req.min).sum().compute()
            logger.debug("start above max")
            above_max = (channel_data >= req.max).sum().compute()

            below_min_ratio = below_min / channel_data.size
            above_max_ratio = above_max / channel_data.size

            logger.debug("start unique")
            # Get unique value counts
            unique_values = da.unique(channel_data).compute()

            logger.debug("start unique below min")
            # Calculate ratio of min/max values among unique values
            below_min_in_unique_ratio = (unique_values <= req.min).sum() / unique_values.size

            logger.debug("start unique above max")

            above_max_in_unique_ratio = (unique_values >= req.max).sum() / unique_values.size

            logger.debug("done res")

            res = GetMinMaxSummaryRes(
                below_min_count=below_min,
                above_max_count=above_max,
                below_min_ratio=below_min_ratio,
                above_max_ratio=above_max_ratio,
                below_min_in_unique_ratio=below_min_in_unique_ratio,
                above_max_in_unique_ratio=above_max_in_unique_ratio,
            )

            return res

    def get_min_max_by_quantile(self, req: GetMinMaxByQuantileReq):
        """Calculate min/max summary based on a ratio threshold.

        Args:
            req: Contains ratio (0-1) and whether to use unique values for calculation

        Returns:
            GetMinMaxSummaryRes with min/max summary using quantile values
        """
        if not isinstance(req, GetMinMaxByQuantileReq):
            req = GetMinMaxByQuantileReq.model_validate(req)

        with TdbReader(req.tdb_uri) as tdb:
            channel_id = req.channel_id
            channel_slice = slice(channel_id, channel_id + 1)
            select_one_ch = ArraySelector(
                slices=AxesData[slice].from_dict(
                    {
                        Axes.C: channel_slice,
                    }
                )
            )

            channel_with_axes = tdb.query(array_selector=select_one_ch)
            channel_data = channel_with_axes.data

            # Get summary using calculated quantiles
            quantile = []
            need_min = req.min_quantile is not None
            need_max = req.max_quantile is not None
            if need_min:
                min_quantile = req.min_quantile
                quantile.append(min_quantile)

            if need_max:
                max_quantile = 1 - req.max_quantile
                quantile.append(max_quantile)

            res = GetMinMaxByQuantileRes()
            if quantile:
                if req.in_unique:
                    # Use unique values for quantile calculation
                    channel_data_da = da.from_array(channel_data)
                    tgt_arr = da.unique(channel_data_da).compute()
                else:
                    tgt_arr = channel_data

                values_by_quantile = np.quantile(tgt_arr, quantile, method="lower").tolist()

                if need_min:
                    res.min = values_by_quantile[0]
                if need_max:
                    res.max = values_by_quantile[0] if len(values_by_quantile) == 1 else values_by_quantile[1]
            return res

    def convert_bit_depth(self, req: BitDepthConvertReq):
        if not isinstance(req, BitDepthConvertReq):
            req = BitDepthConvertReq.model_validate(req)
        src_tdb_uri = self.img_svc.get_tdb_group_uri(req.src_img_id)
        src_tdb = TdbReader(src_tdb_uri)
        with BitDepthConvertReader(src_tdb, tgt_bit_depth=req.tgt_bit_depth) as convert_reader:
            self.ingest_svc.to_tdb(convert_reader, req.tgt_img_id)

    def save_as(self, req: SaveAsReq):
        if not isinstance(req, SaveAsReq):
            req = SaveAsReq.model_validate(req)

        match req.tgt_type:
            case SupportedSaveAsType.tiff:
                self._save_as_tiff(req)
            case SupportedSaveAsType.irs:
                self._save_as_irs(req)
            case SupportedSaveAsType.jpg:
                self._save_as_colored(req)
            case _:
                raise DomainError(
                    DomainErrorCode.export_not_support,
                    save_as_type=req.tgt_type,
                )

    def _save_as_tiff(self, req: SaveAsReq):
        src_tdb_uri = self.img_svc.get_tdb_group_uri(req.src_img_id)
        src_tdb = TdbReader(src_tdb_uri)
        for channel_output in req.channel_outputs:
            output_path = self.img_svc.get_item_store_path(channel_output.sub_path)
            reader_region = req.get_reader_region(channel_output.channel_ids)
            with RegionReader(src_tdb, reader_region) as region_reader:
                tiff_writer = OMETiffWriter(output_path)
                tiff_writer.write_from(region_reader)

    def _save_as_irs(self, req: SaveAsReq):
        src_tdb_uri = self.img_svc.get_tdb_group_uri(req.src_img_id)
        src_tdb = TdbReader(src_tdb_uri)
        for channel_output in req.channel_outputs:
            tgt_file_name = channel_output.sub_path
            reader_region = req.get_reader_region(channel_output.channel_ids)
            with RegionReader(src_tdb, reader_region) as region_reader:
                self.ingest_svc.to_tdb(region_reader, tgt_file_name)

    def _save_as_colored(self, req: SaveAsReq):
        src_tdb_uri = self.img_svc.get_tdb_group_uri(req.src_img_id)
        src_tdb = TdbReader(src_tdb_uri)
        for channel_output in req.channel_outputs:
            output_path = self.img_svc.get_item_store_path(channel_output.sub_path)
            reader_region = req.get_reader_region(channel_output.channel_ids)
            with RegionReader(src_tdb, reader_region) as region_reader:
                colored_img_writer = ColoredImgWriter(output_path)
                colored_img_writer.write_from(region_reader)

    def test(self, sleep: int):
        logger.info("start for {}", sleep)
        expire = datetime.now() + timedelta(seconds=sleep)
        while datetime.now() < expire:
            import time

            time.sleep(2)
            logger.info("running => {} {}", datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ"), sleep)

        logger.info("done => {} {}", datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ"), sleep)
        return sleep

    async def test_async(self, sleep: int):
        logger.info("start for {}", sleep)
        expire = datetime.now() + timedelta(seconds=sleep)
        while datetime.now() < expire:
            await asyncio.sleep(2)
            logger.info("running => {} {}", datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ"), sleep)

        logger.info("done => {} {}", datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ"), sleep)
        return sleep

    def test_err(self):
        try:
            a = 1 / 0
        except Exception as err:
            raise DomainError(DomainErrorCode.item_operation_not_support) from err
